// 图片样式 - 使用高优先级选择器替代 !important
.ProseMirror.ProseMirror {
  // 全局隐藏 ProseMirror 辅助元素，防止光标闪烁
  .ProseMirror-separator,
  .ProseMirror-trailingBreak {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
    pointer-events: none !important;
  }

  p {
    > img,
    > .resizable-image-wrapper {
      max-width: 80%;
      min-width: 5rem;
      margin: 0; /* 移除外边距，改用伪元素实现间距 */
      padding: 0;
      display: inline-block; /* 改为 inline-block，避免 flex 布局影响光标定位 */
      width: auto;
      border-radius: 0.25rem;
      position: relative;
      text-align: center;
      vertical-align: middle; /* 改为 middle，更好的垂直对齐 */
      transform: translateY(0);
      transition: none;
      line-height: 1; /* 恢复正常行高，避免影响光标高度 */
      will-change: transform;
      white-space: nowrap;
      font-size: 0; /* 添加字体大小为0，消除底部空白 */

      /* 使用伪元素在图片后创建间距，不影响控制点定位 */
      &::after {
        content: '';
        display: inline-block;
        width: 0.5rem; /* 为光标创建间距 */
        height: 1em;
        vertical-align: middle;
        pointer-events: none;
        position: absolute;
        right: -0.5rem; /* 定位在图片包装器外部 */
        top: 50%;
        transform: translateY(-50%);
      }

      img {
        height: auto;
        border-radius: 0.25rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 10%);
        position: relative;
        z-index: 1;
        display: block;
        margin: 0;
        padding: 0;
        transform: translateY(0);
        transition: none;
        will-change: transform;
        line-height: 0; /* 添加行高为0，消除底部空白 */
        font-size: 0; /* 添加字体大小为0，消除底部空白 */

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 15%);
        }
      }

      &.ProseMirror-selectednode {
        outline: 3px solid var(--blue);
        outline-offset: 0;
        border: none;
        box-shadow:
          0 0 0 1px rgba(45, 140, 240, 50%),
          0 0 12px rgba(45, 140, 240, 30%);
        z-index: 2;
      }

      .resize-handle {
        display: none;
        opacity: 0;
        transition:
          opacity 0.2s ease,
          transform 0.2s ease;
      }

      &.ProseMirror-selectednode .resize-handle {
        display: block;
        opacity: 1;
        animation: handle-fade-in 0.2s ease forwards;
      }

      @keyframes handle-fade-in {
        from {
          opacity: 0;
          transform: scale(0.8);
        }

        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      // 小屏幕设备的响应式布局优化
      @media (width <= 768px) {
        max-width: 100%;
        min-width: unset;

        img {
          max-width: 100%;
          width: auto;
          object-fit: contain;
        }
      }
    }

    // 段落中包含图片时的光标优化
    &:has(.resizable-image-wrapper),
    &:has(img) {
      min-height: 2em; /* 增加最小高度，确保光标有足够空间 */
      position: relative;
      line-height: 1.5; /* 设置合适的行高 */

      // 为光标提供稳定的定位环境
      &::after {
        content: '';
        display: inline-block;
        width: 0.5rem; /* 增加宽度，为光标提供更多空间 */
        height: 1.2em;
        vertical-align: middle;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        background: transparent; /* 确保背景透明 */
      }
    }

    // 图片后的元素添加间距
    .resizable-image-wrapper + *,
    img + * {
      margin-left: 0.25rem; /* 增加后续元素的左边距 */
    }

    // 确保图片后有足够的空间用于光标显示
    .resizable-image-wrapper,
    img {
      /* 在图片后添加一个透明的空间，专门用于光标定位 */
      &::before {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        margin-right: 0.125rem;
        vertical-align: middle;
      }
    }

    &:only-child {
      > img,
      > .resizable-image-wrapper {
        position: relative;
        display: inline-block;

        &.ProseMirror-selectednode {
          outline: 2px solid #2d8cf0;

          .resize-handle {
            display: block;
            position: absolute;
            z-index: 200;
          }
        }
      }
    }
  }

  // 列表中的图片样式
  ul,
  ol {
    li {
      p {
        img,
        .resizable-image-wrapper {
          max-width: 100%;
          min-width: 5rem;
          margin: 0; /* 移除外边距，使用伪元素 */
          display: inline-block;
          border-radius: 0.25rem;
          vertical-align: middle;
          position: relative;
          transition:
            outline 0.2s ease,
            box-shadow 0.2s ease;

          /* 为列表中的图片也添加间距伪元素 */
          &::after {
            content: '';
            display: inline-block;
            width: 0.5rem;
            height: 1em;
            vertical-align: middle;
            pointer-events: none;
            position: absolute;
            right: -0.5rem;
            top: 50%;
            transform: translateY(-50%);
          }

          &.ProseMirror-selectednode {
            outline: 2px solid #2d8cf0;
            outline-offset: 0;
            box-shadow:
              0 0 0 1px rgba(45, 140, 240, 50%),
              0 0 8px rgba(45, 140, 240, 30%);
            transform: translateZ(0);

            .resize-handle {
              display: block;
              position: absolute;
              z-index: 200;
              animation: handle-appear 0.2s ease forwards;
            }

            @keyframes handle-appear {
              from {
                opacity: 0;
                transform: scale(0.8);
              }

              to {
                opacity: 1;
                transform: scale(1);
              }
            }
          }
        }
      }
    }
  }

  // 任务列表中的图片样式
  ul[data-type='taskList'] {
    li {
      > div {
        img,
        .resizable-image-wrapper {
          max-width: 100%;
          min-width: 5rem;
          margin: 0; /* 移除外边距，使用伪元素 */
          display: inline-block;
          border-radius: 0.25rem;
          vertical-align: middle;
          position: relative;
          transition: all 0.2s ease;

          /* 为任务列表中的图片也添加间距伪元素 */
          &::after {
            content: '';
            display: inline-block;
            width: 0.5rem;
            height: 1em;
            vertical-align: middle;
            pointer-events: none;
            position: absolute;
            right: -0.5rem;
            top: 50%;
            transform: translateY(-50%);
          }

          &.ProseMirror-selectednode {
            outline: 2px solid #2d8cf0;
            outline-offset: 0;
            box-shadow:
              0 0 0 1px rgba(45, 140, 240, 50%),
              0 0 8px rgba(45, 140, 240, 30%);
            transform: translateZ(0);

            .resize-handle {
              display: block;
              position: absolute;
              z-index: 200;
              animation: resize-handle-in 0.2s ease forwards;
            }

            @keyframes resize-handle-in {
              from {
                opacity: 0;
                transform: scale(0.8);
              }

              to {
                opacity: 1;
                transform: scale(1);
              }
            }
          }
        }
      }
    }
  }
}

// 只读模式下的图片样式

/* 只读模式下的图片样式 - 需要强制覆盖编辑模式样式，保留必要的 !important */
.editor-readonly.editor-readonly {
  .ProseMirrorInput,
  .ProseMirror.ProseMirror {
    /* 确保所有图片容器没有边框和轮廓 */
    .resizable-image-wrapper,
    .resizable-image-wrapper.ProseMirror-selectednode,
    .resizable-image-wrapper[class*='ProseMirror'],
    div > .resizable-image-wrapper,
    p > .resizable-image-wrapper,
    li > .resizable-image-wrapper {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: pointer;

      /* 确保调整大小的控制点不可见 */
      .resize-handle,
      .resize-handle[class*='handle-'] {
        display: none !important;
        visibility: hidden !important;
        pointer-events: none !important;
        opacity: 0 !important;
      }

      /* 悬停效果 - 已读状态下取消阴影效果 */
      &:hover {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
      }

      /* 图片元素样式 */
      img,
      img.ProseMirror-selectednode,
      img[class*='ProseMirror'] {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
        cursor: pointer;

        &:hover,
        &:focus,
        &:active {
          outline: none !important;
          border: none !important;
          box-shadow: none !important;
        }
      }
    }

    /* 直接添加到编辑器内容的图片 - 覆盖编辑模式下的悬浮效果 */
    > img,
    p > img,
    li > img,
    div > img,
    img.ProseMirror-selectednode,
    img[class*='ProseMirror'] {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: pointer;

      &:hover,
      &:focus,
      &:active {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
      }
    }

    /* 特别针对段落中的图片，覆盖编辑模式的悬浮效果 */
    p > img:hover,
    p > .resizable-image-wrapper img:hover {
      box-shadow: none !important;
    }
  }
}

// 图片预览模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 85%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  cursor: zoom-out;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  padding: 2rem;
  box-sizing: border-box;
  backdrop-filter: blur(3px);
}

.modal-overlay-active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay img {
  max-width: 95%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 0.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 25%);
  transform: scale(0.95);
  opacity: 0;
  transition:
    opacity 0.5s ease,
    transform 0.3s ease;
}

.modal-overlay-active img {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner {
  border: 0.25rem solid rgba(255, 255, 255, 20%);
  border-top: 0.25rem solid #f0f0f0;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 针对移动设备的优化 - 移除控制点相关样式，统一使用桌面样式
@media (width <= 768px) {
  .ProseMirror.ProseMirror {
    // 控制点样式已统一，不再需要移动设备特殊处理

    .resizable-image-wrapper.ProseMirror-selectednode {
      outline-width: 2px;
    }

    p,
    li,
    div {
      > img,
      > .resizable-image-wrapper {
        max-width: 100%;
        min-width: unset;
        margin: 0; /* 移除外边距，使用伪元素 */
        padding: 0;

        /* 移动设备上的间距伪元素 */
        &::after {
          content: '';
          display: inline-block;
          width: 0.75rem; /* 移动设备上更大的间距 */
          height: 1em;
          vertical-align: middle;
          pointer-events: none;
          position: absolute;
          right: -0.75rem;
          top: 50%;
          transform: translateY(-50%);
        }

        img {
          max-width: 100%;
          margin: 0;
          padding: 0;
          width: auto;
          object-fit: contain;
        }
      }
    }
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-overlay img {
    max-width: 100%;
  }

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}

// 光标可见性增强和定位优化
.ProseMirror.ProseMirror {
  // 增强光标可见性
  caret-color: #2d8cf0 !important;

  // 光标周围的文本选择优化
  ::selection {
    background-color: rgba(45, 140, 240, 25%);
    color: inherit;
  }

  // 防止光标位置跳跃的额外措施
  &.ProseMirror-focused {
    // 确保聚焦时光标颜色正确
    caret-color: #2d8cf0 !important;

    // 图片附近的光标稳定性
    p:has(.resizable-image-wrapper),
    p:has(img) {
      // 提供更稳定的光标定位基准
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
      }
    }
  }

  // 专门针对图片后光标定位的优化
  p {
    // 确保图片后的光标有明确的定位空间
    .resizable-image-wrapper + br,
    img + br {
      display: inline-block;
      width: 0.5rem;
      height: 1em;
      vertical-align: middle;
    }

    // 为图片所在行的末尾提供光标定位空间
    &:has(.resizable-image-wrapper):not(:has(.resizable-image-wrapper + *)),
    &:has(img):not(:has(img + *)) {
      &::after {
        content: '\00a0'; /* 不间断空格，为光标提供定位点 */
        display: inline-block;
        width: 0.5rem;
        height: 1em;
        vertical-align: middle;
        color: transparent;
        pointer-events: none;
        font-size: 1rem;
        line-height: 1;
      }
    }
  }
}

// 浏览器兼容性支持
@supports not (selector(:has(*))) {
  .ProseMirror.ProseMirror {
    p {
      // 为不支持 :has() 的浏览器提供备用方案
      min-height: 2em; /* 增加最小高度 */
      position: relative;
      line-height: 1.5;

      .resizable-image-wrapper,
      img {
        margin: 0; /* 移除外边距 */

        // 为所有图片后添加光标定位空间
        &::after {
          content: '';
          display: inline-block;
          width: 0.5rem;
          height: 1em;
          vertical-align: middle;
          pointer-events: none;
          position: absolute;
          right: -0.5rem;
          top: 50%;
          transform: translateY(-50%);
        }
      }

      // 为所有段落末尾添加光标定位点
      &::after {
        content: '\00a0';
        display: inline-block;
        width: 0.5rem;
        height: 1em;
        vertical-align: middle;
        color: transparent;
        pointer-events: none;
        font-size: 1rem;
        line-height: 1;
      }
    }
  }
}

// 特定浏览器的光标优化
@supports (-moz-appearance: none) {
  .ProseMirror.ProseMirror {
    caret-color: #2d8cf0 !important;
  }
}

@supports (-webkit-appearance: none) {
  .ProseMirror.ProseMirror {
    caret-color: #2d8cf0 !important;
  }
}

// 移动设备的光标优化
@media (hover: none) and (pointer: coarse) {
  .ProseMirror.ProseMirror {
    p {
      // 移动设备上增加段落最小高度
      min-height: 2.5em;

      // 移动设备上的段落末尾光标定位
      &:has(.resizable-image-wrapper):not(:has(.resizable-image-wrapper + *)),
      &:has(img):not(:has(img + *)) {
        &::after {
          content: '\00a0';
          display: inline-block;
          width: 0.75rem; /* 移动设备上更大的空间 */
          height: 1.2em;
          vertical-align: middle;
          color: transparent;
          pointer-events: none;
          font-size: 1rem;
          line-height: 1;
        }
      }
    }
  }
}
