# 图片光标问题修复说明

## 问题描述

在 Tiptap 编辑器中，当点击图片所在行的后方空白区域时，会出现以下问题：

1. **光标闪烁问题**：光标会向后方一段距离闪烁，然后回到原来的位置
2. **光标重叠问题**：光标与图片紧挨得太近，导致几乎重叠，光标显示不明显
3. **ProseMirror 辅助元素**：图片下方会出现 `ProseMirror-separator` 和 `ProseMirror-trailingBreak` 元素，可能导致闪烁

## 根本原因分析

### 1. ProseMirror 辅助元素
- `ProseMirror-separator`：ProseMirror 用于分隔不同节点的辅助元素
- `ProseMirror-trailingBreak`：用于在行末提供换行支持的辅助元素
- 这些元素在图片后方创建了额外的可点击区域，导致光标定位不准确

### 2. 光标定位机制
- ProseMirror 在处理内联元素（如图片）时，会在元素前后创建光标位置
- 当点击图片后方空白时，光标会尝试定位到最近的有效位置
- 由于缺乏明确的定位基准，光标会出现闪烁和跳跃

### 3. 视觉重叠问题
- 图片与光标之间缺乏足够的视觉间距
- 光标颜色可能与图片边缘颜色相近，导致可见性差

## 解决方案

### 1. 隐藏辅助元素
```scss
.ProseMirror-separator,
.ProseMirror-trailingBreak {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  pointer-events: none !important;
}
```

### 2. 增加图片间距和改善布局
```scss
.resizable-image-wrapper,
img {
  margin-right: 0.5rem; // 增加右侧间距，确保光标有足够空间
  display: inline-block; // 改为 inline-block，避免 flex 布局影响
  vertical-align: middle; // 更好的垂直对齐
  line-height: 1; // 恢复正常行高

  // 为图片后添加光标定位空间
  &::after {
    content: '';
    display: inline-block;
    width: 0.25rem;
    height: 1em;
    vertical-align: middle;
    pointer-events: none;
  }
}
```

### 3. 段落光标定位优化
```scss
p:has(.resizable-image-wrapper),
p:has(img) {
  min-height: 2em; // 增加最小高度
  position: relative;
  line-height: 1.5; // 设置合适的行高

  // 为图片所在行末尾提供光标定位点
  &:not(:has(.resizable-image-wrapper + *)):not(:has(img + *)) {
    &::after {
      content: '\00a0'; // 不间断空格作为光标定位点
      display: inline-block;
      width: 0.5rem;
      height: 1em;
      vertical-align: middle;
      color: transparent;
      pointer-events: none;
    }
  }
}
```

### 4. 光标可见性增强
```scss
.ProseMirror {
  caret-color: #2d8cf0 !important;

  // 专门针对图片后光标定位的优化
  p .resizable-image-wrapper + br,
  p img + br {
    display: inline-block;
    width: 0.5rem;
    height: 1em;
    vertical-align: middle;
  }
}
```

### 5. 移动设备特殊优化
```scss
@media (hover: none) and (pointer: coarse) {
  .ProseMirror p {
    min-height: 2.5em; // 移动设备更大的最小高度

    .resizable-image-wrapper,
    img {
      margin-right: 0.75rem; // 移动设备更大的间距
    }
  }
}
```

## 浏览器兼容性

### CSS `:has()` 选择器支持
- 现代浏览器：Chrome 105+, Firefox 121+, Safari 15.4+
- 提供了不支持 `:has()` 的浏览器的备用方案

### 光标颜色支持
- 使用 `@supports` 查询确保在不同浏览器中的兼容性
- 支持 Firefox (`-moz-appearance`) 和 WebKit (`-webkit-appearance`)

## 测试验证

### 基础测试
1. 在编辑器中插入图片
2. 点击图片所在行的后方空白区域
3. 验证光标不再闪烁到远处
4. 确认光标与图片有适当间距（至少 0.5rem）
5. 检查开发者工具中没有可见的 `ProseMirror-separator` 和 `ProseMirror-trailingBreak` 元素

### 详细测试场景
1. **单独图片测试**
   - 在空段落中插入图片
   - 点击图片右侧空白区域
   - 光标应该稳定显示在图片右侧，不重叠

2. **图片后有文字测试**
   - 插入图片后添加文字
   - 点击图片和文字之间的空隙
   - 光标应该准确定位，不闪烁

3. **列表中的图片测试**
   - 在有序/无序列表中插入图片
   - 测试光标定位是否正常

4. **任务列表中的图片测试**
   - 在任务列表项中插入图片
   - 验证光标行为

5. **移动设备测试**
   - 在移动设备或触摸屏上测试
   - 确认有足够的触摸区域

### 预期结果
- ✅ 光标与图片之间有明显的视觉间距
- ✅ 点击图片右侧时光标不会跳跃或闪烁
- ✅ 光标颜色为蓝色 (#2d8cf0)，清晰可见
- ✅ 在所有浏览器中表现一致
- ✅ 移动设备上有更大的操作空间

## 注意事项

1. **性能影响**：隐藏辅助元素不会影响 ProseMirror 的功能，只是视觉上的优化
2. **编辑功能**：所有图片编辑功能（调整大小、选择等）保持不变
3. **响应式设计**：在移动设备上提供了更大的间距以适应触摸操作
4. **向后兼容**：修改不会影响现有的图片内容和功能
