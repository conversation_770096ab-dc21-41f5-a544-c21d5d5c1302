# 图片光标问题修复说明

## 问题描述

在 Tiptap 编辑器中，图片组件存在以下问题：

### 原始问题
1. **光标闪烁问题**：光标会向后方一段距离闪烁，然后回到原来的位置
2. **光标重叠问题**：光标与图片紧挨得太近，导致几乎重叠，光标显示不明显
3. **ProseMirror 辅助元素**：图片下方会出现 `ProseMirror-separator` 和 `ProseMirror-trailingBreak` 元素，可能导致闪烁

### 修复后衍生的问题
4. **蓝边闪烁问题**：点击图片时会出现一瞬间的选中然后又取消选中，导致蓝色边框闪烁
5. **意外的 bubble-menu**：图片的选中/取消选中行为会触发 bubble-menu 的显示
6. **光标位置跳跃**：点击图片后光标会跳到图片前方

## 解决方案

### 1. 图片扩展配置修复 (`ImageExtension.ts`)

- **禁用选择功能**：设置 `selectable: false`，防止图片被意外选中
- **保持其他功能**：保留拖拽、内联等必要功能

### 2. 样式修复 (`cursor-fix.scss`)

- **隐藏辅助元素**：完全隐藏 `ProseMirror-separator` 和 `ProseMirror-trailingBreak` 元素
- **增加图片间距**：为图片右侧添加 `0.25rem` 的间距，为光标留出视觉空间
- **光标定位优化**：通过伪元素为光标提供稳定的定位基准
- **最小高度保证**：确保包含图片的段落有足够的最小高度来显示光标
- **防止选中**：使用 `user-select: none` 防止图片被意外选中

### 3. 图片样式调整 (`image.scss`)

- **右侧间距**：修改图片包装器的 `margin` 为 `0 0.25rem 0 0`
- **后续元素间距**：为图片后的元素添加 `margin-left: 0.125rem`
- **段落优化**：为包含图片的段落添加定位基准和最小高度
- **禁用选中样式**：移除图片的默认选中状态样式，防止蓝边闪烁

### 3. 光标样式增强 (`cursor.scss`)

- **可见性增强**：确保光标颜色为 `#2d8cf0`，提高可见性
- **定位稳定性**：为图片附近的光标提供更稳定的定位环境
- **浏览器兼容性**：支持不同浏览器的光标显示

### 4. Bubble Menu 修复 (`EditorBubbleMenu.vue`)

- **图片检测增强**：不仅检查选中的节点，还检查选择范围内是否包含图片
- **防止误触发**：确保任何涉及图片的选择都不会触发 bubble-menu

### 5. 节点视图处理

#### TypeScript 版本 (`ImageNodeView.ts`)
- **DOM 清理**：添加 `cleanupProseMirrorElements` 函数，主动移除辅助元素
- **生命周期处理**：在初始化、更新和销毁时清理辅助元素
- **点击事件优化**：添加 `handleImageClick` 函数，防止编辑模式下的意外选中

#### Vue 版本 (`ImageNodeView.vue`)
- **组合式函数**：添加清理函数到 Vue 组件的生命周期钩子
- **响应式清理**：使用 `nextTick` 确保 DOM 更新后进行清理
- **点击处理统一**：统一处理图片点击事件，区分编辑模式和只读模式

## 技术细节

### CSS 选择器优先级
使用高优先级选择器 `.ProseMirror.ProseMirror` 确保样式能够覆盖默认样式。

### 浏览器兼容性
- 使用 `@supports` 查询为不支持 `:has()` 的浏览器提供备用方案
- 针对 Firefox 和 WebKit 浏览器的特定优化

### 移动设备优化
在触摸设备上增加更多间距，提供更好的用户体验。

## 测试方法

1. 使用 `ImageCursorTest.vue` 测试组件
2. 在编辑器中插入图片
3. 点击图片所在行的后方空白区域
4. 观察光标行为是否正常
5. 检查开发者工具中是否还有辅助元素

## 注意事项

- 修复后的样式可能会影响图片的整体布局，需要测试各种场景
- 清理辅助元素的操作是异步的，使用 `setTimeout` 确保 DOM 更新完成
- 在非编辑模式下，这些修复同样有效，确保只读模式的用户体验

## 相关文件

- `cursor-fix.scss` - 主要的光标修复样式
- `image.scss` - 图片样式调整
- `cursor.scss` - 光标样式增强
- `ImageNodeView.ts` - TypeScript 节点视图处理
- `ImageNodeView.vue` - Vue 节点视图处理
- `ImageCursorTest.vue` - 测试组件
