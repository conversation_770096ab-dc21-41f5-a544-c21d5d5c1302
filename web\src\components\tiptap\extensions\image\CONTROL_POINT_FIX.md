# 图片控制点定位和动画修复

## 问题描述

1. **控制点定位偏移**：缩放控制点的边框位置不对，右边突出，整体向右偏移
2. **选中动画闪烁**：第一次点击图片会出现蓝边闪烁

## 修复方案

### 1. 控制点定位修复

#### 问题原因
- 图片容器 `.resizable-image-wrapper` 的尺寸计算不准确
- 控制点相对定位受到容器的 padding/margin 影响
- 容器的 box-sizing 设置可能影响尺寸计算

#### 解决方案
```scss
.resizable-image-wrapper {
  position: relative;
  display: inline-block;
  max-width: 100%;
  box-sizing: border-box;
  /* 确保容器尺寸精确匹配图片 */
  width: fit-content;
  height: fit-content;
  /* 确保没有额外的间距影响控制点定位 */
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}
```

#### 控制点样式强化
```scss
.resize-handle {
  position: absolute;
  /* 强制重置可能影响定位的属性 */
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
  box-sizing: border-box;
}
```

### 2. 动画效果移除

#### 移除的动画
- 选中时的蓝边闪烁动画
- 控制点的淡入动画
- 滚动到视图中心的动画
- 所有过渡效果

#### 修改的文件
- `ImageNodeView.vue` - 移除选中时的动画逻辑
- `image.scss` - 移除控制点和选中状态的动画
- `image-node-view.scss` - 移除控制点过渡效果

### 3. 光标间距优化

#### 简化的光标处理
```scss
.resizable-image-wrapper,
img {
  &::after {
    content: '\00a0'; /* 使用不间断空格 */
    font-size: 0.5em;
    width: 0.25rem;
    display: inline-block;
    pointer-events: none;
  }
}
```

## 技术要点

1. **容器尺寸**：使用 `width: fit-content` 和 `height: fit-content` 确保容器精确匹配图片尺寸
2. **控制点定位**：使用 `!important` 强制重置可能影响定位的属性
3. **动画移除**：完全移除所有动画和过渡效果，保持简洁的用户体验
4. **光标处理**：使用不间断空格而不是复杂的伪元素定位

## 测试方法

使用 `ImageCursorTest.vue` 测试组件验证：
1. 控制点是否正确对齐图片边缘
2. 第一次点击图片是否没有蓝边闪烁
3. 光标是否在图片后正确显示
4. 是否还有不必要的动画效果

## 注意事项

- 所有修改都保持了原有功能的完整性
- 只是优化了视觉效果和用户体验
- 如果问题仍然存在，可能需要检查其他影响因素：
  - 浏览器的默认样式
  - 父容器的样式影响
  - JavaScript 动态修改的样式
