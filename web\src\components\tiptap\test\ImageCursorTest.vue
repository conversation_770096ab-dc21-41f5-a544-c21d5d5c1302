<template>
  <div class="image-cursor-test">
    <h2>图片光标测试页面</h2>
    <p>测试图片附近的光标行为和 ProseMirror 辅助元素处理</p>
    
    <div class="test-editor">
      <TiptapEditor
        v-model="content"
        :editable="true"
        :extensions="['image', 'paragraph', 'text']"
        placeholder="在这里添加图片并测试光标行为..."
        class="test-editor-content"
      />
    </div>

    <div class="test-instructions">
      <h3>测试说明：</h3>
      <ol>
        <li>在编辑器中插入一张图片</li>
        <li>点击图片所在行的后方空白区域</li>
        <li>观察光标是否还会闪烁到远处</li>
        <li>检查光标与图片的间距是否合适</li>
        <li>确认没有 ProseMirror-separator 和 ProseMirror-trailingBreak 元素显示</li>
        <li><strong>新增测试：</strong>点击图片本身，确认不会出现蓝边闪烁</li>
        <li><strong>新增测试：</strong>点击图片后，确认不会意外显示 bubble-menu</li>
        <li><strong>新增测试：</strong>双击图片应该正常预览</li>
        <li><strong>新增测试：</strong>在非编辑模式下，单击图片应该预览</li>
      </ol>
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <pre>{{ JSON.stringify(content, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TiptapEditor from '@/components/tiptap/TiptapEditor.vue'

const content = ref({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是一个测试段落，后面会有一张图片。'
        }
      ]
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '图片示例：'
        },
        {
          type: 'image',
          attrs: {
            src: '/api/file/resource/example.jpg',
            alt: '测试图片',
            width: '200px'
          }
        },
        {
          type: 'text',
          text: ' 图片后的文本'
        }
      ]
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '请在图片所在行点击测试光标行为。'
        }
      ]
    }
  ]
})
</script>

<style scoped>
.image-cursor-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.test-editor {
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 1rem 0;
  min-height: 300px;
}

.test-editor-content {
  padding: 1rem;
}

.test-instructions {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-instructions ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-instructions li {
  margin: 0.5rem 0;
}

.debug-info {
  background-color: #f1f3f4;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.debug-info pre {
  background-color: #fff;
  padding: 0.5rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
  max-height: 300px;
  overflow-y: auto;
}

h2 {
  color: #2d8cf0;
  margin-bottom: 1rem;
}

h3 {
  color: #333;
  margin: 1rem 0 0.5rem 0;
}
</style>
