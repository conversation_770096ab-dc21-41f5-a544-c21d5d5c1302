<template>
  <div class="image-cursor-test">
    <h2>图片光标测试页面</h2>
    <p>测试图片附近的光标行为和 ProseMirror 辅助元素处理</p>
    
    <div class="test-editor">
      <TiptapEditor
        v-model="content"
        :editable="true"
        :extensions="['image', 'paragraph', 'text']"
        placeholder="在这里添加图片并测试光标行为..."
        class="test-editor-content"
      />
    </div>

    <div class="test-instructions">
      <h3>测试说明：</h3>
      <ol>
        <li>在编辑器中插入一张图片</li>
        <li>点击图片所在行的后方空白区域</li>
        <li>观察光标是否还会闪烁到远处</li>
        <li>检查光标与图片的间距是否合适（应该有 0.25rem 的间距）</li>
        <li>确认没有 ProseMirror-separator 和 ProseMirror-trailingBreak 元素显示</li>
        <li>测试在列表和任务列表中的图片光标行为</li>
      </ol>
    </div>

    <div class="test-status">
      <h3>修复状态：</h3>
      <ul>
        <li>✅ 隐藏 ProseMirror-separator 和 ProseMirror-trailingBreak 元素</li>
        <li>✅ 为图片右侧添加 0.25rem 间距</li>
        <li>✅ 增强光标可见性（#2d8cf0 颜色）</li>
        <li>✅ 为包含图片的段落添加最小高度和定位基准</li>
        <li>✅ 支持列表和任务列表中的图片</li>
        <li>✅ 移动设备兼容性</li>
        <li>✅ 浏览器兼容性（包括不支持 :has() 的浏览器）</li>
      </ul>
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <pre>{{ JSON.stringify(content, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TiptapEditor from '@/components/tiptap/TiptapEditor.vue'

const content = ref({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是一个测试段落，用于测试图片光标行为。请在此处插入图片并测试光标定位。'
        }
      ]
    }
  ]
})
</script>

<style scoped>
.image-cursor-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-editor {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin: 1rem 0;
  min-height: 200px;
}

.test-editor-content {
  padding: 1rem;
}

.test-instructions {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-instructions ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-status {
  background-color: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-status ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-status li {
  margin: 0.25rem 0;
}

.debug-info {
  background-color: #f0f0f0;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.debug-info pre {
  margin: 0;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-word;
}

h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

h3 {
  color: #555;
  margin: 0 0 0.5rem 0;
}

p {
  color: #666;
  line-height: 1.5;
}
</style>
