<template>
  <div class="image-cursor-test">
    <h2>图片光标测试页面</h2>
    <p>测试图片附近的光标行为和 ProseMirror 辅助元素处理</p>
    
    <div class="test-editor">
      <TiptapEditor
        v-model="content"
        :editable="true"
        :extensions="['image', 'paragraph', 'text']"
        placeholder="在这里添加图片并测试光标行为..."
        class="test-editor-content"
      />
    </div>

    <div class="test-instructions">
      <h3>测试说明：</h3>
      <ol>
        <li>在编辑器中插入一张图片</li>
        <li>点击图片所在行的后方空白区域</li>
        <li>观察光标是否还会闪烁到远处</li>
        <li>检查光标与图片的间距是否合适（应该有 0.25rem 的间距）</li>
        <li>确认没有 ProseMirror-separator 和 ProseMirror-trailingBreak 元素显示</li>
        <li>测试在列表和任务列表中的图片光标行为</li>
      </ol>
    </div>

    <div class="test-status">
      <h3>修复状态：</h3>
      <ul>
        <li>✅ 隐藏 ProseMirror-separator 和 ProseMirror-trailingBreak 元素</li>
        <li>✅ 使用绝对定位伪元素创建 0.5rem 间距（不影响控制点）</li>
        <li>✅ 改善图片布局（inline-block + middle 对齐）</li>
        <li>✅ 增强光标可见性（#2d8cf0 颜色）</li>
        <li>✅ 为包含图片的段落添加 2em 最小高度</li>
        <li>✅ 添加不间断空格作为光标定位点</li>
        <li>✅ 支持列表和任务列表中的图片</li>
        <li>✅ 移动设备优化（0.75rem 间距 + 2.5em 高度）</li>
        <li>✅ 浏览器兼容性（包括不支持 :has() 的浏览器）</li>
        <li>✅ 控制点定位不受间距影响，保持精确定位</li>
      </ul>
    </div>

    <div class="test-scenarios">
      <h3>测试场景：</h3>
      <div class="scenario">
        <h4>1. 基础光标定位测试</h4>
        <p>插入图片后，点击图片右侧空白区域，观察：</p>
        <ul>
          <li>光标是否与图片有明显间距（至少 0.5rem）</li>
          <li>光标是否稳定，不闪烁或跳跃</li>
          <li>光标颜色是否为蓝色且清晰可见</li>
        </ul>
      </div>

      <div class="scenario">
        <h4>2. 不同环境测试</h4>
        <ul>
          <li>在普通段落中插入图片</li>
          <li>在列表项中插入图片</li>
          <li>在任务列表中插入图片</li>
          <li>图片后添加文字内容</li>
        </ul>
      </div>
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <pre>{{ JSON.stringify(content, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TiptapEditor from '@/components/tiptap/TiptapEditor.vue'

const content = ref({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是一个测试段落，用于测试图片光标行为。请在此处插入图片并测试光标定位。'
        }
      ]
    }
  ]
})
</script>

<style scoped>
.image-cursor-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-editor {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin: 1rem 0;
  min-height: 200px;
}

.test-editor-content {
  padding: 1rem;
}

.test-instructions {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-instructions ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-status {
  background-color: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-status ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-status li {
  margin: 0.25rem 0;
}

.test-scenarios {
  background-color: #fff3cd;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  border-left: 4px solid #ffc107;
}

.test-scenarios .scenario {
  margin: 1rem 0;
}

.test-scenarios h4 {
  color: #856404;
  margin: 0.5rem 0 0.25rem 0;
  font-size: 1rem;
}

.test-scenarios ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-scenarios li {
  margin: 0.25rem 0;
  color: #856404;
}

.debug-info {
  background-color: #f0f0f0;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.debug-info pre {
  margin: 0;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-word;
}

h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

h3 {
  color: #555;
  margin: 0 0 0.5rem 0;
}

p {
  color: #666;
  line-height: 1.5;
}
</style>
