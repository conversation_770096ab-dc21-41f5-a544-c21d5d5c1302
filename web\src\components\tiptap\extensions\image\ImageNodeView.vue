<template>
  <div
    class="resizable-image-wrapper"
    :class="{
      'readonly-image': !isEditable,
      'ProseMirror-selectednode': isSelected && isEditable,
      resizing: isResizing,
    }"
    ref="resizableWrapper"
    contenteditable="false"
    @click="handleWrapperClick"
  >
    <img
      ref="imageElement"
      :src="imageSrc"
      :alt="node.attrs.alt || ''"
      contenteditable="false"
      :style="{ width: node.attrs.width || '', height: node.attrs.height || '' }"
      loading="eager"
      :data-relative-src="relativeSrc"
      :data-original-src="relativeSrc"
      @dblclick="handleImageDoubleClick"
      @click="handleImageClick"
    />

    <!-- 调整大小的控制点 -->
    <div
      v-for="(_, position) in handles"
      :key="position"
      :class="['resize-handle', `handle-${position}`]"
      :data-handle-position="position"
      :data-center-handle="isCenterHandle(position) ? 'true' : 'false'"
      v-show="isEditable && isSelected"
      @mousedown="(e) => handleResizeStart(e, position)"
      @touchstart="(e) => handleTouchStart(e, position)"
    ></div>

    <!-- 尺寸信息显示 -->
    <div v-show="isResizing" class="resize-info">
      {{ Math.round(currentWidth) }} × {{ Math.round(currentHeight) }}
    </div>

    <!-- 选中区域，用于触发图片选中而不影响图片本身的点击 -->
    <div
      v-if="isEditable && !isSelected"
      class="image-select-area"
      @click="handleSelectAreaClick"
      title="点击选中图片以显示调整大小控制点"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

import { useImagePreview } from './composables/useImagePreview'
import { useImageResize } from './composables/useImageResize'
import { extractRelativePath, getFullImageUrl } from './ImageResourceManager'

import type { Editor } from '@tiptap/vue-3'
import './styles/image-node-view.scss'

interface NodeType {
  attrs: {
    src: string
    alt?: string
    width?: string
    height?: string
  }
}

const props = defineProps<{
  node: NodeType
  editor: Editor
  getPos: () => number
  useThumbnail: boolean
}>()

// 图片相关引用和状态
const resizableWrapper = ref<HTMLDivElement | null>(null)
const imageElement = ref<HTMLImageElement | null>(null)
const isEditable = computed(() => props.editor.isEditable)
const isSelected = ref(false)
const aspectRatio = ref(1)

// 获取相对路径
const relativeSrc = computed(() => extractRelativePath(props.node.attrs.src))
const imageSrc = computed(() => getFullImageUrl(relativeSrc.value, props.useThumbnail))

// 使用图片调整大小组合式函数
let resizeComposable: ReturnType<typeof useImageResize>

// 清理 ProseMirror 辅助元素的函数
const cleanupProseMirrorElements = () => {
  if (resizableWrapper.value?.parentElement) {
    const parentElement = resizableWrapper.value.parentElement
    const separators = parentElement.querySelectorAll('.ProseMirror-separator, .ProseMirror-trailingBreak')
    separators.forEach(element => {
      if (element.parentNode === parentElement) {
        element.remove()
      }
    })
  }
}

// 在 onMounted 中初始化，确保 imageElement 已经存在
const initializeResizeComposable = () => {
  if (imageElement.value) {
    resizeComposable = useImageResize({
      editor: props.editor,
      getPos: props.getPos,
      imageElement: imageElement.value,
    })
  }
}

// 临时状态，在组合式函数初始化前使用
const isResizing = ref(false)
const currentWidth = ref(0)
const currentHeight = ref(0)
const handles = {
  'top-left': null,
  'top-right': null,
  'bottom-left': null,
  'bottom-right': null,
  top: null,
  right: null,
  bottom: null,
  left: null,
} as Record<string, null>

// 代理方法，确保组合式函数已初始化
const handleResizeStart = (e: MouseEvent, position: string) => {
  if (resizeComposable) {
    resizeComposable.handleResizeStart(e, position)
  }
}

const handleTouchStart = (e: TouchEvent, position: string) => {
  if (resizeComposable) {
    resizeComposable.handleTouchStart(e, position)
  }
}

const isCenterHandle = (position: string) => {
  if (resizeComposable) {
    return resizeComposable.isCenterHandle(position)
  }
  return ['top', 'right', 'bottom', 'left'].includes(position)
}

// 监听编辑器更新事件
watch(
  () => props.editor.isEditable,
  (newValue) => {
    if (!newValue) {
      isSelected.value = false
    }
  },
)

// 处理图片包装器点击事件，允许选中但控制行为
const handleWrapperClick = (e: MouseEvent) => {
  if (!isEditable.value) return

  // 检查点击的是否是控制点
  const target = e.target as HTMLElement
  if (target.classList.contains('resize-handle')) {
    // 点击控制点时，允许正常的调整大小行为
    return
  }

  // 检查是否点击的是图片本身
  if (target.tagName === 'IMG') {
    // 点击图片本身时，阻止默认的选中行为
    e.preventDefault()
    e.stopPropagation()
    return false
  }

  // 点击图片包装器的其他区域时，允许选中图片以显示控制点
  const pos = props.getPos()
  if (typeof pos === 'number') {
    props.editor.commands.setNodeSelection(pos)
  }
}

// 处理图片点击事件，防止意外选中和 bubble-menu 显示
const handleImageClick = (e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation()

  if (!isEditable.value) {
    // 非编辑模式下单击预览
    handlePreview(e)
  } else {
    // 编辑模式下，点击图片本身不触发选中
    return false
  }
}

// 处理选中区域点击事件
const handleSelectAreaClick = (e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation()

  // 选中图片以显示控制点
  const pos = props.getPos()
  if (typeof pos === 'number') {
    props.editor.commands.setNodeSelection(pos)
  }
}

// 处理图片双击事件
const handleImageDoubleClick = (e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation()

  if (isEditable.value) {
    // 编辑模式下双击预览
    handlePreview(e)
  }
}

// 处理图片预览
const handlePreview = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  e.stopPropagation()

  // 使用图片预览组合式函数
  const { showImagePreview } = useImagePreview()
  showImagePreview({
    src: imageSrc.value,
    originalSrc: relativeSrc.value,
    alt: props.node.attrs.alt || '图片预览',
    useThumbnail: props.useThumbnail,
    onOriginalLoaded: (originalUrl: string) => {
      // 更新图片元素为原图
      if (imageElement.value && relativeSrc.value.includes('/thumbnail')) {
        imageElement.value.src = originalUrl

        // 更新原始路径，移除thumbnail标记
        const newOriginalSrc = relativeSrc.value.replace('/thumbnail', '')
        imageElement.value.dataset.originalSrc = newOriginalSrc

        // 如果需要，可以在这里更新节点属性
        if (typeof props.getPos === 'function' && !props.editor.isEditable) {
          try {
            props.editor.commands.updateAttributes('image', {
              src: newOriginalSrc,
            })
          } catch {}
        }
      }
    },
  })
}

// 调整大小相关方法已移至 useImageResize 组合式函数

// 监听选中状态
onMounted(() => {
  // 初始化调整大小组合式函数
  initializeResizeComposable()

  // 初始清理 ProseMirror 辅助元素
  nextTick(() => {
    cleanupProseMirrorElements()
  })

  if (resizableWrapper.value) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement
          const wasSelected = isSelected.value
          isSelected.value = target.classList.contains('ProseMirror-selectednode')

          // 如果是新选中的图片，添加视觉反馈
          if (!wasSelected && isSelected.value && isEditable.value) {
            // 平滑滚动到视图中心
            resizableWrapper.value?.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            })

            // 添加短暂高亮效果
            const element = resizableWrapper.value
            if (element) {
              element.animate(
                [
                  { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 3px rgba(45, 140, 240, 0.2)' },
                  { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 5px rgba(45, 140, 240, 0.5)' },
                  { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 3px rgba(45, 140, 240, 0.2)' },
                ],
                {
                  duration: 600,
                  easing: 'ease-in-out',
                },
              )
            }
          }

          // 非编辑模式下移除选中状态
          if (!isEditable.value) {
            isSelected.value = false
          }
        }
      })
    })

    observer.observe(resizableWrapper.value, {
      attributes: true,
      attributeFilter: ['class'],
    })

    // 清理函数
    onBeforeUnmount(() => {
      observer.disconnect()
      // 最终清理 ProseMirror 辅助元素
      cleanupProseMirrorElements()
    })
  }

  // 初始化图片尺寸
  if (imageElement.value) {
    const rect = imageElement.value.getBoundingClientRect()
    currentWidth.value = rect.width
    currentHeight.value = rect.height
    aspectRatio.value = rect.width / rect.height
  }
})
</script>
