<template>
  <div
    class="resizable-image-wrapper"
    :class="{
      'readonly-image': !isEditable,
      'ProseMirror-selectednode': isSelected && isEditable,
      resizing: isResizing,
    }"
    ref="resizableWrapper"
    contenteditable="false"
  >
    <img
      ref="imageElement"
      :src="imageSrc"
      :alt="node.attrs.alt || ''"
      contenteditable="false"
      :style="{ width: node.attrs.width || '', height: node.attrs.height || '' }"
      loading="eager"
      :data-relative-src="relativeSrc"
      :data-original-src="relativeSrc"
      @dblclick="isEditable && handlePreview"
      @click="!isEditable && handlePreview"
    />

    <!-- 调整大小的控制点 -->
    <div
      v-for="(_, position) in handles"
      :key="position"
      :class="['resize-handle', `handle-${position}`]"
      :data-handle-position="position"
      :data-center-handle="isCenterHandle(position) ? 'true' : 'false'"
      v-show="isEditable && isSelected"
      @mousedown="(e) => handleResizeStart(e, position)"
      @touchstart="(e) => handleTouchStart(e, position)"
    ></div>

    <!-- 尺寸信息显示 -->
    <div v-show="isResizing" class="resize-info">
      {{ Math.round(currentWidth) }} × {{ Math.round(currentHeight) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

import { useImagePreview } from './composables/useImagePreview'
import { useImageResize } from './composables/useImageResize'
import { extractRelativePath, getFullImageUrl } from './ImageResourceManager'

import type { Editor } from '@tiptap/vue-3'
import './styles/image-node-view.scss'

interface NodeType {
  attrs: {
    src: string
    alt?: string
    width?: string
    height?: string
  }
}

const props = defineProps<{
  node: NodeType
  editor: Editor
  getPos: () => number
  useThumbnail: boolean
}>()

// 图片相关引用和状态
const resizableWrapper = ref<HTMLDivElement | null>(null)
const imageElement = ref<HTMLImageElement | null>(null)
const isEditable = computed(() => props.editor.isEditable)
const isSelected = ref(false)
const aspectRatio = ref(1)

// 获取相对路径
const relativeSrc = computed(() => extractRelativePath(props.node.attrs.src))
const imageSrc = computed(() => getFullImageUrl(relativeSrc.value, props.useThumbnail))

// 使用图片调整大小组合式函数
let resizeComposable: ReturnType<typeof useImageResize>

// 清理 ProseMirror 辅助元素的函数
const cleanupProseMirrorElements = () => {
  if (resizableWrapper.value?.parentElement) {
    const parentElement = resizableWrapper.value.parentElement
    const separators = parentElement.querySelectorAll('.ProseMirror-separator, .ProseMirror-trailingBreak')
    separators.forEach(element => {
      if (element.parentNode === parentElement) {
        element.remove()
      }
    })
  }
}

// 在 onMounted 中初始化，确保 imageElement 已经存在
const initializeResizeComposable = () => {
  if (imageElement.value) {
    resizeComposable = useImageResize({
      editor: props.editor,
      getPos: props.getPos,
      imageElement: imageElement.value,
    })
  }
}

// 临时状态，在组合式函数初始化前使用
const isResizing = ref(false)
const currentWidth = ref(0)
const currentHeight = ref(0)
const handles = {
  'top-left': null,
  'top-right': null,
  'bottom-left': null,
  'bottom-right': null,
  top: null,
  right: null,
  bottom: null,
  left: null,
} as Record<string, null>

// 代理方法，确保组合式函数已初始化
const handleResizeStart = (e: MouseEvent, position: string) => {
  if (resizeComposable) {
    resizeComposable.handleResizeStart(e, position)
  }
}

const handleTouchStart = (e: TouchEvent, position: string) => {
  if (resizeComposable) {
    resizeComposable.handleTouchStart(e, position)
  }
}

const isCenterHandle = (position: string) => {
  if (resizeComposable) {
    return resizeComposable.isCenterHandle(position)
  }
  return ['top', 'right', 'bottom', 'left'].includes(position)
}

// 监听编辑器更新事件
watch(
  () => props.editor.isEditable,
  (newValue) => {
    if (!newValue) {
      isSelected.value = false
    }
  },
)

// 处理图片预览
const handlePreview = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  e.stopPropagation()

  // 使用图片预览组合式函数
  const { showImagePreview } = useImagePreview()
  showImagePreview({
    src: imageSrc.value,
    originalSrc: relativeSrc.value,
    alt: props.node.attrs.alt || '图片预览',
    useThumbnail: props.useThumbnail,
    onOriginalLoaded: (originalUrl: string) => {
      // 更新图片元素为原图
      if (imageElement.value && relativeSrc.value.includes('/thumbnail')) {
        imageElement.value.src = originalUrl

        // 更新原始路径，移除thumbnail标记
        const newOriginalSrc = relativeSrc.value.replace('/thumbnail', '')
        imageElement.value.dataset.originalSrc = newOriginalSrc

        // 如果需要，可以在这里更新节点属性
        if (typeof props.getPos === 'function' && !props.editor.isEditable) {
          try {
            props.editor.commands.updateAttributes('image', {
              src: newOriginalSrc,
            })
          } catch {}
        }
      }
    },
  })
}

// 调整大小相关方法已移至 useImageResize 组合式函数

// 监听选中状态
onMounted(() => {
  // 初始化调整大小组合式函数
  initializeResizeComposable()

  // 初始清理 ProseMirror 辅助元素
  nextTick(() => {
    cleanupProseMirrorElements()
  })

  if (resizableWrapper.value) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement
          const wasSelected = isSelected.value
          isSelected.value = target.classList.contains('ProseMirror-selectednode')

          // 移除所有选中时的动画效果，保持简洁
          // 选中状态的样式已经通过 CSS 类 ProseMirror-selectednode 处理

          // 非编辑模式下移除选中状态
          if (!isEditable.value) {
            isSelected.value = false
          }
        }
      })
    })

    observer.observe(resizableWrapper.value, {
      attributes: true,
      attributeFilter: ['class'],
    })

    // 清理函数
    onBeforeUnmount(() => {
      observer.disconnect()
      // 最终清理 ProseMirror 辅助元素
      cleanupProseMirrorElements()
    })
  }

  // 初始化图片尺寸
  if (imageElement.value) {
    const rect = imageElement.value.getBoundingClientRect()
    currentWidth.value = rect.width
    currentHeight.value = rect.height
    aspectRatio.value = rect.width / rect.height
  }
})
</script>
