/**
 * 图片光标定位修复样式
 * 解决图片附近光标闪烁和重叠问题
 */

.ProseMirror {
  // 全局隐藏 ProseMirror 辅助元素，防止光标闪烁
  .ProseMirror-separator,
  .ProseMirror-trailingBreak {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
    pointer-events: none !important;
  }

  // 段落中包含图片时的光标优化
  p {
    // 确保包含图片的段落有足够的最小高度
    &:has(.resizable-image-wrapper),
    &:has(img) {
      min-height: 1.5em;
      position: relative;
      
      // 为光标提供稳定的定位环境
      &::after {
        content: '';
        display: inline-block;
        width: 0;
        height: 1em;
        vertical-align: baseline;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
      }
    }

    // 图片后的元素添加间距
    .resizable-image-wrapper + *,
    img + * {
      margin-left: 0.25rem;
    }

    // 图片元素本身的光标间距优化
    .resizable-image-wrapper,
    img {
      // 右侧添加小间距，为光标留出空间
      margin-right: 0.25rem;
      
      // 确保图片不会干扰光标定位
      &::after {
        content: '';
        display: inline-block;
        width: 0.125rem;
        height: 0;
        vertical-align: baseline;
      }
    }
  }

  // 增强光标可见性
  caret-color: #2d8cf0 !important;
  
  // 光标周围的文本选择优化
  ::selection {
    background-color: rgba(45, 140, 240, 25%);
    color: inherit;
  }

  // 防止光标位置跳跃的额外措施
  &.ProseMirror-focused {
    // 确保聚焦时光标颜色正确
    caret-color: #2d8cf0 !important;
    
    // 图片附近的光标稳定性
    p:has(.resizable-image-wrapper),
    p:has(img) {
      // 提供更稳定的光标定位基准
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
      }
    }
  }
}

// 浏览器兼容性支持
@supports not (selector(:has(*))) {
  .ProseMirror {
    p {
      // 为不支持 :has() 的浏览器提供备用方案
      min-height: 1.5em;
      position: relative;
      
      .resizable-image-wrapper,
      img {
        margin-right: 0.25rem;
      }
    }
  }
}

// 特定浏览器的光标优化
@supports (-moz-appearance: none) {
  .ProseMirror {
    caret-color: #2d8cf0 !important;
  }
}

@supports (-webkit-appearance: none) {
  .ProseMirror {
    caret-color: #2d8cf0 !important;
  }
}

// 移动设备的光标优化
@media (hover: none) and (pointer: coarse) {
  .ProseMirror {
    p {
      .resizable-image-wrapper,
      img {
        // 移动设备上增加更多间距
        margin-right: 0.5rem;
      }
    }
  }
}
