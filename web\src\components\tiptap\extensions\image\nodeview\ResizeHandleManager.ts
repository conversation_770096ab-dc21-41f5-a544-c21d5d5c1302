import { createApp, type App } from 'vue'

import ResizeHandle from '../debug/DebugResizeHandle.vue'

/**
 * 调整大小控制点管理器
 * 负责创建、管理和定位图片调整大小的控制点
 * 重构为使用 Vue 组件而非直接 DOM 操作
 */
export class ResizeHandleManager {
  private handles: Record<string, { app: App; element: HTMLElement } | null> = {
    topLeft: null,
    topRight: null,
    bottomLeft: null,
    bottomRight: null,
    top: null,
    right: null,
    bottom: null,
    left: null,
  }

  private isMobileDevice: boolean
  private onResizeStart?: (event: MouseEvent | TouchEvent, position: string) => void

  constructor(
    private resizableWrapper: HTMLDivElement,
    isMobileDevice: boolean,
    onResizeStart?: (event: MouseEvent | TouchEvent, position: string) => void,
  ) {
    this.isMobileDevice = isMobileDevice
    this.onResizeStart = onResizeStart
  }

  /**
   * 创建调整大小的控制点（使用 Vue 组件）
   */
  createResizeHandle(position: string): { app: App; element: HTMLElement } {
    // 创建容器元素
    const container = document.createElement('div')

    // 创建 Vue 应用实例
    const app = createApp(ResizeHandle, {
      position,
      isMobileDevice: this.isMobileDevice,
      visible: false, // 默认隐藏
      onResizeStart: this.onResizeStart,
    })

    // 挂载组件
    app.mount(container)

    return {
      app,
      element: container.firstElementChild as HTMLElement,
    }
  }

  /**
   * 更新控制点可见性
   */
  private updateHandleVisibility(handle: { app: App; element: HTMLElement }, visible: boolean) {
    // 通过更新组件 props 来控制可见性
    const component = handle.app._instance
    if (component && component.props) {
      component.props.visible = visible
      component.update()
    }
  }

  /**
   * 设置所有控制点
   */
  setupResizeHandles(): Record<string, HTMLElement | null> {
    // 创建角和中间的控制点
    this.handles.topLeft = this.createResizeHandle('top-left')
    this.handles.topRight = this.createResizeHandle('top-right')
    this.handles.bottomLeft = this.createResizeHandle('bottom-left')
    this.handles.bottomRight = this.createResizeHandle('bottom-right')

    // 创建中间控制点
    this.handles.top = this.createResizeHandle('top')
    this.handles.right = this.createResizeHandle('right')
    this.handles.bottom = this.createResizeHandle('bottom')
    this.handles.left = this.createResizeHandle('left')

    // 将控制点添加到包装器
    Object.values(this.handles).forEach((handle) => {
      if (handle) {
        this.resizableWrapper.appendChild(handle.element)
      }
    })

    // 返回元素引用以保持向后兼容
    const elementHandles: Record<string, HTMLElement | null> = {}
    Object.entries(this.handles).forEach(([key, handle]) => {
      elementHandles[key] = handle?.element || null
    })

    return elementHandles
  }

  /**
   * 显示所有控制点
   */
  showAllHandles() {
    Object.values(this.handles).forEach((handle) => {
      if (handle) {
        this.updateHandleVisibility(handle, true)
      }
    })
  }

  /**
   * 隐藏所有控制点
   */
  hideAllHandles() {
    Object.values(this.handles).forEach((handle) => {
      if (handle) {
        this.updateHandleVisibility(handle, false)
      }
    })
  }

  /**
   * 获取控制点元素
   */
  getHandles(): Record<string, HTMLElement | null> {
    const elementHandles: Record<string, HTMLElement | null> = {}
    Object.entries(this.handles).forEach(([key, handle]) => {
      elementHandles[key] = handle?.element || null
    })
    return elementHandles
  }

  /**
   * 清理资源
   */
  destroy() {
    Object.values(this.handles).forEach((handle) => {
      if (handle) {
        handle.app.unmount()
        if (handle.element.parentNode) {
          handle.element.parentNode.removeChild(handle.element)
        }
      }
    })
    this.handles = {
      topLeft: null,
      topRight: null,
      bottomLeft: null,
      bottomRight: null,
      top: null,
      right: null,
      bottom: null,
      left: null,
    }
  }
}
