import Img from '@tiptap/extension-image'

import { createNodeView } from '@/components/tiptap/extensions/image/ImageNodeView'
import {
  extractRelativePath,
  getFullImageUrl,
} from '@/components/tiptap/extensions/image/ImageResourceManager'

import type { NodeViewRenderer, NodeViewRendererProps } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { NodeView, Decoration, DecorationSource } from '@tiptap/pm/view'

// 定义图片节点的属性类型
interface ImageAttributes {
  src: string
  alt?: string
  width?: string
  height?: string
}

// 定义自定义的 NodeView 类型
interface CustomNodeView extends NodeView {
  update: (
    node: ProseMirrorNode,
    decorations: readonly Decoration[],
    innerDecorations: DecorationSource,
  ) => boolean
}

/**
 * 创建一个基础图片扩展的工厂函数，接收是否使用缩略图的配置
 * @param useThumbnail 是否使用缩略图
 * @returns 扩展实例
 */
export const createImageExtension = (useThumbnail = false) => {
  return Img.extend({
    draggable: true, // 使图片可拖动
    inline: true, // 改为内联元素，解决与列表项的内容模型冲突
    group: 'inline', // 将图片添加到内联组，以便在列表中使用
    selectable: false, // 禁用图片的直接选中功能，防止点击时意外选中

    addOptions() {
      return {
        ...this.parent?.(),
        // 添加转换图片路径的方法
        transformSrc: (src: string) => extractRelativePath(src),
        getFullUrl: (src: string) => getFullImageUrl(src, useThumbnail),
      }
    },

    addAttributes() {
      return {
        ...this.parent?.(),
        width: {
          default: null,
          renderHTML: (attributes) => {
            if (!attributes.width) {
              return {}
            }
            return {
              width: attributes.width,
            }
          },
        },
        height: {
          default: null,
          renderHTML: (attributes) => {
            if (!attributes.height) {
              return {}
            }
            return {
              height: attributes.height,
            }
          },
        },
        src: {
          default: null,
          parseHTML: (element) => {
            // 从HTML解析时，将src转为相对路径
            const src = element.getAttribute('src') || ''
            return extractRelativePath(src)
          },
          renderHTML: (attributes) => {
            // 渲染HTML时，保持相对路径
            return { src: attributes.src }
          },
        },
      }
    },

    addNodeView() {
      const nodeView = createNodeView(useThumbnail)
      return ((props: NodeViewRendererProps) => {
        const view = nodeView(props)
        return {
          ...view,
          update: (
            node: ProseMirrorNode,
            _decorations: readonly Decoration[],
            _innerDecorations: DecorationSource,
          ) => {
            if (node.type.name !== 'image') return false
            const attrs = node.attrs as ImageAttributes
            view.update({ attrs })
            return true
          },
        } as CustomNodeView
      }) as NodeViewRenderer
    },

    // 添加自定义方法来处理路径，供扩展内部和编辑器使用
    addStorage() {
      return {
        transformSrc: extractRelativePath,
        getFullUrl: (src: string) => getFullImageUrl(src, useThumbnail),
      }
    },
  }).configure({
    allowBase64: false,
  })
}

// 默认使用缩略图版本
const image = createImageExtension(true)

export default image
