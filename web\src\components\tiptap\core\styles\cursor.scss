// 光标样式和动画
@keyframes office-cursor-blink {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

@keyframes office-cursor-appear {
  from {
    opacity: 0;
    height: 0;
  }

  to {
    opacity: 1;
    height: 1.2em;
  }
}

.ProseMirror {
  // 基础光标样式
  .ProseMirror-gapcursor {
    display: none;
    pointer-events: none;
    position: absolute;

    &::after {
      content: '';
      display: block;
      position: absolute;
      width: 3px;
      height: 1.2em;
      background-color: #2d8cf0;
      border-radius: 0;
      animation: office-cursor-blink 1s ease-in-out infinite;
      box-shadow: 0 0 3px rgba(45, 140, 240, 50%);
    }
  }

  // 增强光标可见性
  caret-color: #2d8cf0;

  // 自定义光标
  position: relative;

  // 图片附近的光标优化
  p:has(.resizable-image-wrapper),
  p:has(img) {
    // 确保光标在图片附近有足够的可见性
    caret-color: #2d8cf0;

    // 为光标提供更稳定的定位环境
    &::before {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      width: 2px;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    // 防止光标位置跳跃
    .resizable-image-wrapper,
    img {
      // 确保图片不会干扰光标定位
      &::after {
        content: '';
        display: inline-block;
        width: 0.25rem;
        height: 0;
        vertical-align: baseline;
      }
    }
  }

  // 输入样式仿Office
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  li,
  blockquote {
    &:focus-within:not(:has(.ProseMirror-selectednode)) {
      position: relative;

      &::before {
        display: none;
      }
    }
  }

  // 光标周围文字动画
  .has-cursor-animation {
    animation: text-appear 0.15s ease-out forwards;
  }

  @keyframes text-appear {
    from {
      opacity: 0.7;
      transform: translateY(1px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 覆盖浏览器默认样式
.ProseMirror-focused {
  .ProseMirror-gapcursor {
    display: block;
    animation: office-cursor-appear 0.2s ease-out forwards;
  }
}

// 覆盖浏览器的光标样式
@supports (-moz-appearance: none) {
  .ProseMirror-focused {
    caret-color: #2d8cf0;
  }
}

@supports (-webkit-appearance: none) {
  .ProseMirror-focused {
    caret-color: #2d8cf0;
  }
}
